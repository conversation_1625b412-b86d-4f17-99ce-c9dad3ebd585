import RouteProgressBar from "@/components/common/RouteProgressBar";
import { AuthProvider } from "@/context/AuthContext";
import { SubscriptionProvider } from "@/context/SubscriptionContext";
import { TherapistProvider } from "@/context/TherapistContext";
import GoogleAnalytics from "@/helper/GoogleAnalytics";
import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import { Toaster } from "react-hot-toast";
import "./globals.css";
import HotJar from "@/helper/HotJar";
import Usetiful from "@/helper/Usetiful";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  variable: "--font-poppins",
});

const roboto = Roboto({
  subsets: ["latin"],
  weight: ["100", "300", "400", "500", "700", "900"],
  variable: "--font-roboto",
});

export const metadata: Metadata = {
  title: "Thought Pudding",
  description: "Professional therapy platform connecting you with licensed therapists for secure, confidential mental health sessions.",
  icons: {
    icon: [
      {
        url: '/favicon.ico',
        sizes: '32x32',
        type: 'image/x-icon',
      },
      {
        url: '/assets/images/favicon.png',
        sizes: '32x32',
        type: 'image/png',
      },
      {
        url: '/assets/images/favicon.png',
        sizes: '16x16',
        type: 'image/png',
      },
    ],
    shortcut: '/favicon.ico',
    apple: '/assets/images/favicon.png',
  },
  openGraph: {
    title: "Thought Pudding",
    description: "Professional therapy platform connecting you with licensed therapists for secure, confidential mental health sessions.",
    url: "https://app.thoughtpudding.com",
    siteName: "Thought Pudding",
    type: "website",
    locale: "en_US",
    images: [
      {
        url: '/assets/images/favicon.png?v=2025-01-29',
        width: 512,
        height: 512,
        alt: 'Thought Pudding Logo',
        type: 'image/png',
      },
    ],
  },
  twitter: {
    card: "summary",
    site: "@thoughtpudding",
    creator: "@thoughtpudding",
    title: "Thought Pudding",
    description: "Professional therapy platform connecting you with licensed therapists for secure, confidential mental health sessions.",
    images: ['/assets/images/favicon.png?v=2025-01-29'],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <GoogleAnalytics />
        <HotJar />
        <Usetiful token={"a0522d10b03dba6e442e240943c2fd14"} />
      </head>
      <body className={`${poppins.className} ${poppins.variable} ${roboto.variable} antialiased`}>
        {/* Google Analytics */}
        <RouteProgressBar />
        <Toaster
          position="top-center"
          toastOptions={{
            duration: 5000,
            style: {
              borderRadius: "10px",
              background: "#333",
              color: "#fff",
            },
          }}
          containerClassName="__toast-container"
        />
        <AuthProvider>
          <TherapistProvider>
            <SubscriptionProvider>{children}</SubscriptionProvider>
          </TherapistProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
