import Link from "next/link";
import React from "react";
import {
  GoogleMeetIcon,
  RegularBinIcon,
  RegularNotificationIcon,
} from "../../../../../public/assets/Svgs";
import { formatTime } from "@/utils/axios";
import Image from "next/image";

interface DashboardTBodyProps {
  TableData: Array<unknown>;
  sessionLoading?: boolean;
  setIsRescheduleSession?: (value: boolean) => void;
  isReminderModal?: boolean;
  setIsReminderModal?: (value: boolean) => void;
  isCanceledSessionModal?: boolean;
  setIsCanceledSessionModal?: (value: boolean) => void;
  setSingleSessionID?: (value: string) => void;
  setSingleSessionData: (value: Item) => void;
  therapistData?: {
    minFee?: number;
    maxFee?: number;
  };
}

export interface Item {
  img?: string;
  clientId?: {
    name?: string;
  };
  name?: string;
  meetLink?: string;
  email?: string;
  time?: string;
  status?: string;
  amount?: string | number;
  sessionFee?: string;
  previousFee?: string;
  fromDate?: string;
  toDate?: string;
  tillDate?: string;
  recurrenceDates?: {
    _id: string;
  };
  _id: string;
  fromPublicCalender?: boolean;
  publicCalendarAmountUpdated?: boolean;
  minFee?: number;
  maxFee?: number;
}

const DashboardTBody: React.FC<DashboardTBodyProps> = ({
  TableData = [],
  sessionLoading,
  setIsRescheduleSession,
  setIsReminderModal,
  isReminderModal,
  setIsCanceledSessionModal,
  isCanceledSessionModal,
  setSingleSessionID,
  setSingleSessionData,
}) => {
  return (
    <tbody className="divide-y divide-primary/10">
      {sessionLoading ? (
        // Render skeletons when sessionLoading is true
        [...Array(5)].map((_, index) => (
          <tr key={index} className="animate-pulse">
            <td className="p-15px">
              <div className="flex items-center gap-3">
                <div className="w-[34px] h-[34px] rounded-full bg-gray-200"></div>
                <div>
                  <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-32"></div>
                </div>
              </div>
            </td>
            <td className="p-15px">
              <div className="h-4 bg-gray-200 rounded w-32"></div>
            </td>
            <td className="p-15px">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
            </td>
            <td className="p-15px">
              <div className="h-4 bg-gray-200 rounded w-16"></div>
            </td>
            <td className="p-15px">
              <div className="h-4 bg-gray-200 rounded w-16"></div>
            </td>
            <td className="p-15px">
              <div className="h-4 bg-gray-200 rounded w-24"></div>
            </td>
            <td className="p-15px">
              <div className="h-4 bg-gray-200 rounded w-28"></div>
            </td>
            <td className="p-15px">
              <div className="flex items-center gap-5">
                <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
                <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
              </div>
            </td>
          </tr>
        ))
      ) : TableData.length === 0 ? (
        // Display a "Data is not found" message if TableData is empty and not loading
        <tr>
          <td
            colSpan={8}
            className="p-15px text-center text-primary/70 text-sm/5"
          >
            <div className="flex flex-col items-center justify-center pt-16 pb-20">
              <Image
                width={1000}
                height={1000}
                src="/assets/images/dashboard/not-found-session-list.webp"
                alt="no-data"
                className="w-[185px] h-auto"
              />
              <p className="text-xl/6 font-semibold pt-4 text-primary">
                No Sessions Found!
              </p>
            </div>
          </td>
        </tr>
      ) : (
        // Render actual table rows when not loading and data is available
        (TableData as Item[])?.map((item: Item, index: number) => {
          return (
            <tr
              key={index}
              className={`${item.fromPublicCalender ? 'bg-orange-50 group' : ''}`}
            >
              <td className="p-15px relative">
                {item.fromPublicCalender && (
                  <div className="absolute left-0 -top-10 opacity-0 group-hover:opacity-100 transition-all duration-500 ease-in-out pointer-events-none z-50">
                    <div className="bg-white border border-gray-200 shadow-lg rounded-md px-3 py-3 text-xs text-gray-700 w-40 h-16 flex items-center justify-center text-center leading-relaxed relative">
                      Session created by patient through public calendar
                      {/* White arrow pointing down to the row */}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                        <div className="w-0 h-0 border-l-6 border-r-6 border-t-8 border-l-transparent border-r-transparent border-t-white"></div>
                      </div>
                    </div>
                  </div>
                )}
                <div className="flex items-center gap-3 ">
                  <div className="w-[34px] h-[34px] rounded-full border border-primary/20 bg-[#F5F5F7] overflow-hidden flex items-center justify-center">
                    <span className="text-xs_18 text-[#72748D] font-medium uppercase">
                      {item.clientId?.name &&
                        item.clientId?.name
                          .split(" ")
                          .map((n) => n.charAt(0))
                          .join("")}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm/5 font-semibold text-primary flex items-center gap-1.5 capitalize">
                      {item.clientId?.name}{" "}
                      {item?.meetLink &&
                        ["rescheduled", "confirmed"].includes(
                          (item.status ?? "").trim()
                        ) && (
                          <Link
                            href={item?.meetLink}
                            target="_blank"
                            className="w-3.5 h-auto"
                          >
                            <GoogleMeetIcon />
                          </Link>
                        )}
                    </p>
                    <p className="text-xs_18 text-primary/70 font-medium">
                      {item.email}
                    </p>
                  </div>
                </div>
              </td>
              <td className="p-15px text-primary/70 text-sm/5 font-medium uppercase whitespace-nowrap">
                {formatTime(item?.fromDate ?? "")} -{" "}
                {formatTime(item?.toDate ?? "")}
              </td>
              <td className="p-15px">
                <span
                  className={`inline-block py-1.5 px-3 rounded-full text-sm/5 ${
                    item.status && item.status.trim() === "completed"
                      ? "bg-green-200 text-green-500"
                      : item.status &&
                        ["Upcoming", "confirmed"].includes(item.status.trim())
                      ? "bg-orange-200 text-orange-600"
                      : item.status && item.status.trim() === "cancelled"
                      ? "bg-[#FFEDED] text-[#FF5959]"
                      : item.status && item.status.trim() === "rescheduled"
                      ? "bg-yellow-600/10 text-yellow-600"
                      : ""
                  }`}
                >
                  {item.status}
                </span>
              </td>
              <td className="p-15px text-primary text-sm/5 font-semibold">
                {item?.fromPublicCalender && (item?.amount === "0" || item?.amount === 0)
                  ? "Introductory call"
                  : item.minFee && item.maxFee &&
                    (item.publicCalendarAmountUpdated === undefined || item.publicCalendarAmountUpdated === false)
                    ? `₹ ${item?.minFee} - ₹ ${item?.maxFee}`
                    : `₹ ${item?.amount}`
                }
              </td>
              <td className="p-15px text-primary/70 text-sm/5 font-medium">
                {item.sessionFee || "-"}
              </td>
              <td className="p-15px text-primary text-sm/5">
                <div className="flex items-center gap-3">
                  <p className="font-medium">{item.previousFee || "-"}</p>{" "}
                  {item.previousFee === "Pending" && (
                    <div className="relative group cursor-pointer">
                      <span className="w-5 h-5 flex items-center justify-center border-[1.5px] border-primary text-primary font-medium rounded-full">
                        !
                      </span>
                      <div className="absolute w-[322px] py-30px px-5 top-full right-0 rounded-base bg-white shadow-[0px_4px_16px_0px_#2424241A] transition-all duration-500 opacity-0 hidden group-hover:block group-hover:opacity-100 z-10">
                        <h3 className="text-sm/5 text-primary font-medium">
                          Unpaid Session Payment
                        </h3>
                        <ul className="space-y-15px pt-[22px] text-sm/5">
                          <li className="flex items-center justify-between text-primary">
                            <p className="text-xs_18">14th July,2024</p>
                            <span className="py-[2px] px-3 inline-block bg-red-200 text-red-500 rounded-[5px] text-xs/5">
                              Unpaid
                            </span>
                          </li>
                          <li className="flex items-center justify-between text-primary">
                            <p className="text-xs_18">10th July,2024</p>
                            <span className="py-[2px] px-3 inline-block bg-red-200 text-red-500 rounded-[5px] text-xs/5">
                              Unpaid
                            </span>
                          </li>
                          <li className="flex items-center justify-between text-primary">
                            <p className="text-xs_18">7th July,2024</p>
                            <span className="py-[2px] px-3 inline-block bg-red-200 text-red-500 rounded-[5px] text-xs/5">
                              Unpaid
                            </span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
              </td>
              <td className="p-15px">
                <div className="flex items-center gap-6 text-sm/6 font-medium">
                  <Link
                    href={`javascript:void(0)`}
                    className={`underline ${
                      ["completed", "cancelled"].includes(
                        item.status?.trim() ?? ""
                      )
                        ? "text-green-600/50 cursor-not-allowed"
                        : "text-green-600"
                    }`}
                    onClick={() => {
                      if (
                        !["completed", "cancelled"].includes(
                          item.status?.trim() ?? ""
                        )
                      ) {
                        setIsRescheduleSession?.(true);
                        if (item?.recurrenceDates?._id) {
                          setSingleSessionID?.(item?.recurrenceDates?._id);
                        }
                        setSingleSessionData?.(item);
                      }
                    }}
                  >
                    Reschedule
                  </Link>
                  <Link
                    href={`javascript:void(0)`}
                    className={`underline hidden ${
                      ["completed", "cancelled"].includes(
                        item.status?.trim() ?? ""
                      )
                        ? "text-green-600/50 cursor-not-allowed"
                        : "text-green-600"
                    }`}
                  >
                    Start Session
                  </Link>
                </div>
              </td>
              <td className="p-15px">
                {item.status &&
                ["completed", "cancelled"].includes(item.status.trim()) ? (
                  <div className="flex items-center gap-5">
                    <span>-</span>
                    <span>-</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-5">
                    <RegularNotificationIcon
                      className="w-5 h-5 cursor-pointer"
                      pathFillColor="#242424"
                      strokeWidth={`1.5`}
                      onClick={() => {
                        if (item?.recurrenceDates?._id) {
                          setSingleSessionID?.(item?.recurrenceDates?._id);
                        }
                        setSingleSessionData?.(item);
                        setIsReminderModal?.(!isReminderModal);
                      }}
                    />
                    <RegularBinIcon
                      className="w-5 h-5  cursor-pointer"
                      strokeWidth={`1.5`}
                      onClick={() => {
                        setIsCanceledSessionModal?.(!isCanceledSessionModal);
                        if (item?.recurrenceDates?._id) {
                          setSingleSessionID?.(item?.recurrenceDates?._id);
                        }
                      }}
                    />
                  </div>
                )}
              </td>
            </tr>
          );
        })
      )}
    </tbody>
  );
};

export default DashboardTBody;