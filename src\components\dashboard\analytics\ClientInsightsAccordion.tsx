"use client";
import React, { useState } from "react";
import { createPortal } from "react-dom";
import { Plus, Minus } from "@phosphor-icons/react";
import { AccordionSection } from "./types";
import { StrongRelationshipsData, RuptureRepairData, PeopleHelpingData } from "@/services/analytics.service";
import "./scrollbar.css";

interface ClientInsightsAccordionProps {
  sections: AccordionSection[];
  powerClientsData?: StrongRelationshipsData['powerClientsData'];
  riskClientsData?: RuptureRepairData['riskClients'];
  activeClientsData?: PeopleHelpingData['activeClients'];
  fromDate?: Date;
  toDate?: Date;
}

// Tooltip component using portal
const Tooltip: React.FC<{
  children: React.ReactNode;
  content: string;
  show: boolean;
  position: { x: number; y: number }
}> = ({ children, content, show, position }) => {
  if (!show || typeof window === 'undefined') return <>{children}</>;

  return (
    <>
      {children}
      {createPortal(
        <div
          className="fixed px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg pointer-events-none whitespace-nowrap z-[9999]"
          style={{
            left: position.x,
            top: position.y - 40, // Position above the element
            transform: 'translateX(-50%)', // Center horizontally
          }}
        >
          {content}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
        </div>,
        document.body
      )}
    </>
  );
};

const ClientInsightsAccordion: React.FC<ClientInsightsAccordionProps> = ({ sections, powerClientsData, riskClientsData, activeClientsData, fromDate, toDate }) => {
  // Default to having power_clients expanded, but only one at a time
  const [expandedSection, setExpandedSection] = useState<string>('power_clients');
  const [expandedSubSections, setExpandedSubSections] = useState<string[]>([]);

  // Tooltip state
  const [tooltip, setTooltip] = useState<{
    show: boolean;
    content: string;
    position: { x: number; y: number };
  }>({
    show: false,
    content: '',
    position: { x: 0, y: 0 }
  });

  // Function to format date range
  const formatDateRange = (from?: Date, to?: Date) => {
    if (!from || !to) return 'the chosen time frame';

    const formatDate = (date: Date) => {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    };

    return `${formatDate(from)} - ${formatDate(to)}`;
  };

  // Function to generate avatar initials from name
  const generateAvatar = (name: string) => {
    if (!name) return '';

    const nameParts = name.trim().split(' ');
    if (nameParts.length === 1) {
      // Single name - return first character
      return nameParts[0].charAt(0).toUpperCase();
    } else {
      // Multiple names - return first character of first and last name
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    }
  };

  // Function to check if email needs truncation and truncate if needed
  const truncateEmail = (email: string, maxLength: number = 20) => {
    if (!email || email.length <= maxLength) return email;

    const atIndex = email.indexOf('@');
    if (atIndex === -1) return email;

    const localPart = email.substring(0, atIndex);
    const domainPart = email.substring(atIndex);

    if (localPart.length > maxLength - domainPart.length - 3) {
      const truncatedLocal = localPart.substring(0, maxLength - domainPart.length - 3);
      return `${truncatedLocal}...${domainPart}`;
    }

    return email;
  };

  // Function to check if email is truncated
  const isEmailTruncated = (email: string, maxLength: number = 20) => {
    return email && email.length > maxLength;
  };

  // Tooltip handlers
  const handleMouseEnter = (event: React.MouseEvent, email: string) => {
    if (isEmailTruncated(email)) {
      const rect = event.currentTarget.getBoundingClientRect();
      setTooltip({
        show: true,
        content: email,
        position: {
          x: rect.left + rect.width / 2,
          y: rect.top
        }
      });
    }
  };

  const handleMouseLeave = () => {
    setTooltip({
      show: false,
      content: '',
      position: { x: 0, y: 0 }
    });
  };

  // Categorize risk clients based on their risk flags
  const categorizeRiskClients = () => {
    if (!riskClientsData) {
      return {
        reschedule: [],
        cancellation: [],
        delayedPayment: [],
        cancelledPayment: [],
      };
    }

    const categories = {
      reschedule: riskClientsData.filter(client => client.isRescheduleRisk),
      cancellation: riskClientsData.filter(client => client.isCancellationRisk),
      delayedPayment: riskClientsData.filter(client => client.isDelayedPaymentRisk),
      cancelledPayment: riskClientsData.filter(client => client.isCancelledPaymentRisk),
    };

    return categories;
  };

  const riskCategories = categorizeRiskClients();

  // Find clients with highest cancellation percentage
  const getHighestCancellationClients = () => {
    if (!riskClientsData || riskClientsData.length === 0) return [];

    const maxCancellationPercentage = Math.max(...riskClientsData.map(client => client.cancellationPercentage));
    return riskClientsData.filter(client => client.cancellationPercentage === maxCancellationPercentage);
  };

  // Find clients with highest delayed payment percentage
  const getHighestDelayedPaymentClients = () => {
    if (!riskClientsData || riskClientsData.length === 0) return [];

    const clientsWithDelayedPayments = riskClientsData.filter(client =>
      client.delayedPaymentPercentage !== null && client.delayedPaymentPercentage > 0
    );

    if (clientsWithDelayedPayments.length === 0) return [];

    const maxDelayedPaymentPercentage = Math.max(...clientsWithDelayedPayments.map(client => client.delayedPaymentPercentage!));
    return clientsWithDelayedPayments.filter(client => client.delayedPaymentPercentage === maxDelayedPaymentPercentage);
  };

  // Find common clients between power clients and active clients (most regular clients)
  const getMostRegularClients = () => {
    if (!powerClientsData || !activeClientsData || powerClientsData.length === 0 || activeClientsData.length === 0) return [];

    const commonClients = powerClientsData.filter(powerClient =>
      activeClientsData.some(activeClient => activeClient._id === powerClient.clientId)
    );

    return commonClients;
  };

  const highestCancellationClients = getHighestCancellationClients();
  const highestDelayedPaymentClients = getHighestDelayedPaymentClients();
  const mostRegularClients = getMostRegularClients();

  const toggleSection = (sectionId: string) => {
    // Prevent closing the currently open section - always keep one section open
    // Only allow switching to a different section, never close all
    if (expandedSection !== sectionId) {
      setExpandedSection(sectionId);
      // Clear sub-sections when switching main sections
      setExpandedSubSections([]);
    }
    // If trying to close the current section, do nothing (keep it open)
  };

  const toggleSubSection = (subSectionId: string) => {
    setExpandedSubSections(prev =>
      prev.includes(subSectionId)
        ? prev.filter(id => id !== subSectionId)
        : [...prev, subSectionId]
    );
  };

  const isExpanded = (sectionId: string) => expandedSection === sectionId;
  const isSubExpanded = (subSectionId: string) => expandedSubSections.includes(subSectionId);

  return (
    <div className="space-y-4 max-h-[564px] overflow-hidden flex flex-col font-poppins">
      {sections.map((section) => (
        <div key={section.id} className="rounded-lg overflow-hidden">
          {/* Accordion Header */}
          <button
            onClick={() => toggleSection(section.id)}
            className="w-full p-4 flex items-center justify-between text-left transition-all duration-200 hover:opacity-90 font-poppins"
            style={{ backgroundColor: section.color }}
          >
            <span className="font-medium text-[12px] md:text-[14px] text-gray-800 font-poppins">{section.title}</span>
            {isExpanded(section.id) ? (
              <Minus size={20} className="text-gray-800" />
            ) : (
              <Plus size={20} className="text-gray-800" />
            )}
          </button>

          {/* Accordion Content */}
          {isExpanded(section.id) && (
            <div className="bg-white border-b-4 border-gray-100 flex-1 min-h-0">
              {/* Power Clients Content */}
              {section.id === 'power_clients' && (
                <div className="scrollable-container custom-scrollbar">
                  <div className="scrollable-content p-3 space-y-3 xs:space-y-4">
                  {powerClientsData && powerClientsData.length > 0 ? (
                    powerClientsData.map((client) => (
                      <div key={client.clientId} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                        <div className="flex items-center gap-2 xs:gap-3 min-w-0 flex-1">
                          <div className="w-6 h-6 xs:w-8 xs:h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs xs:text-sm font-medium text-gray-600 flex-shrink-0">
                            {generateAvatar(client.name)}
                          </div>
                          <div className="min-w-0 flex-1">
                            {/* Mobile/Tablet Layout - Vertical Stack */}
                            <div className="md:hidden">
                              <div className="font-medium text-[12px] xs:text-[14px] text-gray-900 truncate">{client.name}</div>
                              <Tooltip
                                content={client.email}
                                show={tooltip.show && tooltip.content === client.email}
                                position={tooltip.position}
                              >
                                <div
                                  className={`text-[11px] xs:text-sm text-gray-500 truncate ${isEmailTruncated(client.email) ? 'cursor-help' : ''}`}
                                  onMouseEnter={(e) => handleMouseEnter(e, client.email)}
                                  onMouseLeave={handleMouseLeave}
                                >
                                  {truncateEmail(client.email)}
                                </div>
                              </Tooltip>
                            </div>

                            {/* Desktop Layout - Horizontal */}
                            <div className="hidden md:flex md:items-center md:justify-between md:w-full">
                              <div className="font-medium text-[14px] text-gray-900 truncate">{client.name}</div>
                              <Tooltip
                                content={client.email}
                                show={tooltip.show && tooltip.content === client.email}
                                position={tooltip.position}
                              >
                                <div
                                  className={`text-sm text-gray-500 truncate ${isEmailTruncated(client.email) ? 'cursor-help' : ''}`}
                                  onMouseEnter={(e) => handleMouseEnter(e, client.email)}
                                  onMouseLeave={handleMouseLeave}
                                >
                                  {truncateEmail(client.email)}
                                </div>
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                        {/* <div className="text-sm font-medium text-gray-700">
                          ₹{client.totalRevenue}
                        </div> */}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-[12px] xs:text-sm text-gray-500">
                      No power clients data available
                    </div>
                  )}
                  </div>
                </div>
              )}

              {/* Risk Clients Content */}
              {section.id === 'risk_clients' && (
                <div className="scrollable-container custom-scrollbar">
                  <div className="scrollable-content p-3 xs:p-4 space-y-3 xs:space-y-4">
                    {/* Most Reschedules */}
                    <div>
                      <div className="flex items-center justify-between mb-2 xs:mb-3">
                        <h4 className="text-[12px] xs:text-sm font-medium text-red-600">Most Reschedules</h4>
                        {riskCategories.reschedule && riskCategories.reschedule.length > 1 && (
                          <button
                            onClick={() => toggleSubSection('reschedules')}
                            className="text-[11px] xs:text-sm text-gray-600 hover:text-gray-800 transition-colors"
                          >
                            {isSubExpanded('reschedules') ? 'Show Less' : `View All (${riskCategories.reschedule.length})`}
                          </button>
                        )}
                      </div>

                      <div className="space-y-2">
                        {riskCategories.reschedule && riskCategories.reschedule.length > 0 ? (
                          (isSubExpanded('reschedules')
                            ? riskCategories.reschedule
                            : riskCategories.reschedule.slice(0, 1)
                          ).map((client) => (
                            <div key={client.clientId} className="flex items-center justify-between py-2">
                              <div className="flex items-center gap-2 xs:gap-3 min-w-0 flex-1">
                                <div className="w-6 h-6 xs:w-8 xs:h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs xs:text-sm font-medium text-gray-600 flex-shrink-0">
                                  {generateAvatar(client.name)}
                                </div>
                                <div className="min-w-0 flex-1">
                                  <div className="font-medium text-[12px] xs:text-[14px] text-gray-900 truncate">{client.name}</div>
                                  <Tooltip
                                    content={client.email}
                                    show={tooltip.show && tooltip.content === client.email}
                                    position={tooltip.position}
                                  >
                                    <div
                                      className={`text-[11px] xs:text-sm text-gray-500 truncate ${isEmailTruncated(client.email) ? 'cursor-help' : ''}`}
                                      onMouseEnter={(e) => handleMouseEnter(e, client.email)}
                                      onMouseLeave={handleMouseLeave}
                                    >
                                      {truncateEmail(client.email)}
                                    </div>
                                  </Tooltip>
                                  {/* Status text below email on small screens */}
                                  <div className="md:hidden text-[11px] xs:text-[14px] text-[#2D313480] mt-1">{client.rescheduledSessions} Reschedules</div>
                                </div>
                              </div>
                              {/* Status text on right side for desktop */}
                              <div className="hidden md:block text-[14px] text-[#2D313480] flex-shrink-0">{client.rescheduledSessions} Reschedules</div>
                            </div>
                          ))
                        ) : (
                          <div className="text-[11px] xs:text-sm text-gray-500">These insights will update as you interact with the platform more</div>
                        )}
                      </div>
                      <div className="border-t border-gray-200 mt-3 xs:mt-4"></div>
                    </div>

                    {/* Most Cancellations */}
                    <div>
                      <div className="flex items-center justify-between mb-2 xs:mb-3">
                        <h4 className="text-[12px] xs:text-sm font-medium text-red-600">Most Cancellations</h4>
                        {riskCategories.cancellation && riskCategories.cancellation.length > 1 && (
                          <button
                            onClick={() => toggleSubSection('cancellations')}
                            className="text-[11px] xs:text-sm text-gray-600 hover:text-gray-800 transition-colors"
                          >
                            {isSubExpanded('cancellations') ? 'Show Less' : `View All (${riskCategories.cancellation.length})`}
                          </button>
                        )}
                      </div>

                      <div className="space-y-2">
                        {riskCategories.cancellation && riskCategories.cancellation.length > 0 ? (
                          (isSubExpanded('cancellations')
                            ? riskCategories.cancellation
                            : riskCategories.cancellation.slice(0, 1)
                          ).map((client) => (
                            <div key={client.clientId} className="flex items-center justify-between py-2">
                              <div className="flex items-center gap-2 xs:gap-3 min-w-0 flex-1">
                                <div className="w-6 h-6 xs:w-8 xs:h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs xs:text-sm font-medium text-gray-600 flex-shrink-0">
                                  {generateAvatar(client.name)}
                                </div>
                                <div className="min-w-0 flex-1">
                                  <div className="font-medium text-[12px] xs:text-[14px] text-gray-900 truncate">{client.name}</div>
                                  <Tooltip
                                    content={client.email}
                                    show={tooltip.show && tooltip.content === client.email}
                                    position={tooltip.position}
                                  >
                                    <div
                                      className={`text-[11px] xs:text-sm text-gray-500 truncate ${isEmailTruncated(client.email) ? 'cursor-help' : ''}`}
                                      onMouseEnter={(e) => handleMouseEnter(e, client.email)}
                                      onMouseLeave={handleMouseLeave}
                                    >
                                      {truncateEmail(client.email)}
                                    </div>
                                  </Tooltip>
                                  {/* Status text below email on small screens */}
                                  <div className="md:hidden text-[11px] xs:text-[14px] text-[#2D313480] font-normal mt-1">{client.cancelledSessions} Cancellation</div>
                                </div>
                              </div>
                              {/* Status text on right side for desktop */}
                              <div className="hidden md:block text-[14px] text-[#2D313480] font-normal flex-shrink-0">{client.cancelledSessions} Cancellation</div>
                            </div>
                          ))
                        ) : (
                          <div className="text-[11px] xs:text-sm text-gray-500">These insights will update as you interact with the platform more</div>
                        )}
                      </div>
                      <div className="border-t border-gray-200 mt-3 xs:mt-4"></div>
                    </div>

                    {/* Most Delayed Payments */}
                    <div>
                      <div className="flex items-center justify-between mb-2 xs:mb-3">
                        <h4 className="text-[12px] xs:text-sm font-medium text-red-600">Most Delayed Payments</h4>
                        {riskCategories.delayedPayment && riskCategories.delayedPayment.length > 1 && (
                          <button
                            onClick={() => toggleSubSection('delayed_payments')}
                            className="text-[11px] xs:text-sm text-gray-600 hover:text-gray-800 transition-colors"
                          >
                            {isSubExpanded('delayed_payments') ? 'Show Less' : `View All (${riskCategories.delayedPayment.length})`}
                          </button>
                        )}
                      </div>

                      <div className="space-y-2">
                        {riskCategories.delayedPayment && riskCategories.delayedPayment.length > 0 ? (
                          (isSubExpanded('delayed_payments')
                            ? riskCategories.delayedPayment
                            : riskCategories.delayedPayment.slice(0, 1)
                          ).map((client) => (
                            <div key={client.clientId} className="flex items-center justify-between py-2">
                              <div className="flex items-center gap-2 xs:gap-3 min-w-0 flex-1">
                                <div className="w-6 h-6 xs:w-8 xs:h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs xs:text-sm font-medium text-gray-600 flex-shrink-0">
                                  {generateAvatar(client.name)}
                                </div>
                                <div className="min-w-0 flex-1">
                                  <div className="font-medium text-[12px] xs:text-[14px] text-gray-900 truncate">{client.name}</div>
                                  <Tooltip
                                    content={client.email}
                                    show={tooltip.show && tooltip.content === client.email}
                                    position={tooltip.position}
                                  >
                                    <div
                                      className={`text-[11px] xs:text-sm text-gray-500 truncate ${isEmailTruncated(client.email) ? 'cursor-help' : ''}`}
                                      onMouseEnter={(e) => handleMouseEnter(e, client.email)}
                                      onMouseLeave={handleMouseLeave}
                                    >
                                      {truncateEmail(client.email)}
                                    </div>
                                  </Tooltip>
                                  {/* Status text below email on small screens */}
                                  <div className="md:hidden text-[11px] text-[14px] text-[#2D313480] mt-1">{client.delayedPayments || 0} Delayed Payments</div>
                                </div>
                              </div>
                              {/* Status text on right side for desktop */}
                              <div className="hidden md:block text-[14px] text-[#2D313480] flex-shrink-0">{client.delayedPayments || 0} Delayed Payments</div>
                            </div>
                          ))
                        ) : (
                          <div className="text-[11px] xs:text-sm text-gray-500">These insights will update as you interact with the platform more</div>
                        )}
                      </div>
                      <div className="border-t border-gray-200 mt-3 xs:mt-4"></div>
                    </div>

                    {/* Most Cancelled Payments */}
                    <div>
                      <div className="flex items-center justify-between mb-2 xs:mb-3">
                        <h4 className="text-[12px] xs:text-sm font-medium text-red-600">Most Cancelled Payments</h4>
                        {riskCategories.cancelledPayment && riskCategories.cancelledPayment.length > 1 && (
                          <button
                            onClick={() => toggleSubSection('cancelled_payments')}
                            className="text-[11px] xs:text-sm text-gray-600 hover:text-gray-800 transition-colors"
                          >
                            {isSubExpanded('cancelled_payments') ? 'Show Less' : `View All (${riskCategories.cancelledPayment.length})`}
                          </button>
                        )}
                      </div>

                      <div className="space-y-2">
                        {riskCategories.cancelledPayment && riskCategories.cancelledPayment.length > 0 ? (
                          (isSubExpanded('cancelled_payments')
                            ? riskCategories.cancelledPayment
                            : riskCategories.cancelledPayment.slice(0, 1)
                          ).map((client) => (
                            <div key={client.clientId} className="flex items-center justify-between py-2">
                              <div className="flex items-center gap-2 xs:gap-3 min-w-0 flex-1">
                                <div className="w-6 h-6 xs:w-8 xs:h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs xs:text-sm font-medium text-gray-600 flex-shrink-0">
                                  {generateAvatar(client.name)}
                                </div>
                                <div className="min-w-0 flex-1">
                                  <div className="font-medium text-[12px] xs:text-[14px] text-gray-900 truncate">{client.name}</div>
                                  <Tooltip
                                    content={client.email}
                                    show={tooltip.show && tooltip.content === client.email}
                                    position={tooltip.position}
                                  >
                                    <div
                                      className={`text-[11px] xs:text-sm text-gray-500 truncate ${isEmailTruncated(client.email) ? 'cursor-help' : ''}`}
                                      onMouseEnter={(e) => handleMouseEnter(e, client.email)}
                                      onMouseLeave={handleMouseLeave}
                                    >
                                      {truncateEmail(client.email)}
                                    </div>
                                  </Tooltip>
                                  {/* Status text below email on small screens */}
                                  <div className="md:hidden text-[11px] xs:text-[14px] text-[#2D313480] mt-1">{client.cancelledPayments || 0} Cancelled Payments</div>
                                </div>
                              </div>
                              {/* Status text on right side for desktop */}
                              <div className="hidden md:block text-[14px] text-[#2D313480] flex-shrink-0">{client.cancelledPayments || 0} Cancelled Payments</div>
                            </div>
                          ))
                        ) : (
                          <div className="text-[11px] xs:text-sm text-gray-500">These insights will update as you interact with the platform more</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* See More Insights Content */}
              {section.id === 'see_more_insights' && (
                <div className="scrollable-container custom-scrollbar">
                  <div className="scrollable-content p-3 space-y-4">
                  {/* Highest Cancellations Insights */}
                  {highestCancellationClients.length > 0 ? (
                    highestCancellationClients.map((client) => (
                      <div key={`cancellation-${client.clientId}`} className="bg-[#FFEEEE] p-2 rounded-lg mb-3">
                        <div className="md:hidden flex items-center mb-2">
                          <div className="w-[120px] xs:w-[153px] h-[26px] xs:h-[30px] bg-[#F2E9FF] rounded-xl flex items-center justify-center">
                            <span className="text-[#000000] text-[10px] xs:text-[12px] font-medium">Highest Cancellations</span>
                          </div>
                        </div>
                        <div className="pt-1 flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <div className="flex items-center gap-2 xs:gap-3 min-w-0 flex-1">
                            <div className="w-[28px] h-[28px] xs:w-[34px] xs:h-[34px] border border-gray-300 rounded-full flex items-center justify-center text-xs xs:text-sm font-medium text-gray-600 flex-shrink-0">
                              {generateAvatar(client.name)}
                            </div>
                            <div className="flex flex-col sm:flex-row sm:gap-3 min-w-0 flex-1">
                              <div className="font-semibold text-[12px] xs:text-[14px] text-[#2D3134] truncate">{client.name}</div>
                              <Tooltip
                                content={client.email}
                                show={tooltip.show && tooltip.content === client.email}
                                position={tooltip.position}
                              >
                                <div
                                  className={`text-[11px] xs:text-[14px] text-[#2D313480] font-medium truncate ${isEmailTruncated(client.email) ? 'cursor-help' : ''}`}
                                  onMouseEnter={(e) => handleMouseEnter(e, client.email)}
                                  onMouseLeave={handleMouseLeave}
                                >
                                  {truncateEmail(client.email)}
                                </div>
                              </Tooltip>
                            </div>
                          </div>
                          <div className="hidden md:flex items-center mt-2 md:mt-0">
                            <div className="w-[153px] h-[30px] bg-[#F2E9FF] rounded-xl flex items-center justify-center">
                              <span className="text-[#000000] text-[12px] font-medium">Highest Cancellations</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-[11px] xs:text-[13px] text-[#000000] font-medium leading-relaxed">
                          <strong>Insight:</strong> {client.name} has had the most cancellations in {formatDateRange(fromDate, toDate)}. It would be helpful to begin exploring this in therapy.
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="bg-[#FFEEEE] p-2 rounded-lg mb-3">
                      <div className="md:hidden flex items-center mb-2">
                        <div className="w-[120px] xs:w-[153px] h-[26px] xs:h-[30px] bg-[#F2E9FF] rounded-xl flex items-center justify-center">
                          <span className="text-[#000000] text-[10px] xs:text-[12px] font-medium">Highest Cancellations</span>
                        </div>
                      </div>
                      <div className="pt-1 flex flex-col md:flex-row md:items-center mb-2">
                        <div className="hidden md:flex items-center">
                          <div className="w-[153px] h-[30px] bg-[#F2E9FF] rounded-xl flex items-center justify-center">
                            <span className="text-[#000000] text-[12px] font-medium">Highest Cancellations</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-[11px] xs:text-[13px] text-[#000000] font-medium leading-relaxed">
                        No cancellations yet (yay)! We&apos;ll flag patterns when there&apos;s more to see.
                      </div>
                    </div>
                  )}

                  {/* Most Regular Client Cards */}
                  {mostRegularClients.length > 0 ? (
                    mostRegularClients.map((client) => (
                      <div key={`regular-${client.clientId}`} className="bg-[#D7FFCE] p-2 rounded-lg mb-3">
                        <div className="md:hidden mb-2">
                          <div className="w-[110px] xs:w-[138px] h-[26px] xs:h-[30px] bg-[#BAFF6F] rounded-xl flex items-center justify-center">
                            <span className="text-[#000000] text-[10px] xs:text-[12px] font-medium">Most Regular Client</span>
                          </div>
                        </div>
                        <div className="pt-1 flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <div className="flex items-center gap-2 xs:gap-3 min-w-0 flex-1">
                            <div className="w-[28px] h-[28px] xs:w-[34px] xs:h-[34px] border border-gray-300 rounded-full flex items-center justify-center text-xs xs:text-sm font-medium text-gray-600 flex-shrink-0">
                              {generateAvatar(client.name)}
                            </div>
                            <div className="flex flex-col sm:flex-row sm:gap-3 min-w-0 flex-1">
                              <div className="font-semibold text-[12px] xs:text-[14px] text-[#2D3134] truncate">{client.name}</div>
                              <Tooltip
                                content={client.email}
                                show={tooltip.show && tooltip.content === client.email}
                                position={tooltip.position}
                              >
                                <div
                                  className={`text-[11px] xs:text-[14px] text-[#2D313480] font-medium truncate ${isEmailTruncated(client.email) ? 'cursor-help' : ''}`}
                                  onMouseEnter={(e) => handleMouseEnter(e, client.email)}
                                  onMouseLeave={handleMouseLeave}
                                >
                                  {truncateEmail(client.email)}
                                </div>
                              </Tooltip>
                            </div>
                          </div>
                          <div className="hidden md:flex items-center mt-2 md:mt-0">
                            <div className="w-[138px] h-[30px] bg-[#BAFF6F] rounded-xl flex items-center justify-center">
                              <span className="text-[#000000] text-[12px] font-medium">Most Regular Client</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-[11px] xs:text-[13px] text-[#000000] font-medium leading-relaxed">
                          <strong>Insight:</strong> {client.name} has been the most regular with their therapy work and
                          payments. Kudos to your work as a therapist 🥳
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="bg-[#D7FFCE] p-2 rounded-lg mb-3">
                      <div className="md:hidden mb-2">
                        <div className="w-[110px] xs:w-[138px] h-[26px] xs:h-[30px] bg-[#BAFF6F] rounded-xl flex items-center justify-center">
                          <span className="text-[#000000] text-[10px] xs:text-[12px] font-medium">Most Regular Client</span>
                        </div>
                      </div>
                      <div className="pt-1 flex flex-col md:flex-row md:items-center mb-2">
                        <div className="hidden md:flex items-center">
                          <div className="w-[138px] h-[30px] bg-[#BAFF6F] rounded-xl flex items-center justify-center">
                            <span className="text-[#000000] text-[12px] font-medium">Most Regular Client</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-[11px] xs:text-[13px] text-[#000000] font-medium leading-relaxed">
                        Your most consistent patients will make an appearance here once you sync your sessions.
                      </div>
                    </div>
                  )}

                  {/* Most Delayed Payments Cards */}
                  {highestDelayedPaymentClients.length > 0 ? (
                    highestDelayedPaymentClients.map((client) => (
                      <div key={`delayed-${client.clientId}`} className="bg-[#FDFFCE] p-2 rounded-lg mb-3">
                        <div className="md:hidden mb-2">
                          <div className="w-[130px] xs:w-[166px] h-[26px] xs:h-[30px] bg-[#FFD16F] rounded-xl flex items-center justify-center">
                            <span className="text-[#000000] text-[10px] xs:text-[12px] font-medium">Most Delayed Payments</span>
                          </div>
                        </div>
                        <div className="pt-1 flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <div className="flex items-center gap-2 xs:gap-3 min-w-0 flex-1">
                            <div className="w-[28px] h-[28px] xs:w-[34px] xs:h-[34px] border border-gray-300 rounded-full flex items-center justify-center text-xs xs:text-sm font-medium text-gray-600 flex-shrink-0">
                              {generateAvatar(client.name)}
                            </div>
                            <div className="flex flex-col sm:flex-row sm:gap-3 min-w-0 flex-1">
                              <div className="font-semibold text-[12px] xs:text-[14px] text-[#2D3134] truncate">{client.name}</div>
                              <Tooltip
                                content={client.email}
                                show={tooltip.show && tooltip.content === client.email}
                                position={tooltip.position}
                              >
                                <div
                                  className={`text-[11px] xs:text-[14px] text-[#2D313480] font-medium truncate ${isEmailTruncated(client.email) ? 'cursor-help' : ''}`}
                                  onMouseEnter={(e) => handleMouseEnter(e, client.email)}
                                  onMouseLeave={handleMouseLeave}
                                >
                                  {truncateEmail(client.email)}
                                </div>
                              </Tooltip>
                            </div>
                          </div>
                          <div className="hidden md:flex items-center mt-2 md:mt-0">
                            <div className="w-[166px] h-[30px] bg-[#FFD16F] rounded-xl flex items-center justify-center">
                              <span className="text-[#000000] text-[12px] font-medium">Most Delayed Payments</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-[11px] xs:text-[13px] text-[#000000] font-medium leading-relaxed">
                          <strong>Insight:</strong> {client.name} has had the most delayed payments in {formatDateRange(fromDate, toDate)}. It would be helpful to begin exploring this in therapy.
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="bg-[#FDFFCE] p-2 rounded-lg mb-3">
                      <div className="md:hidden mb-2">
                        <div className="w-[130px] xs:w-[166px] h-[26px] xs:h-[30px] bg-[#FFD16F] rounded-xl flex items-center justify-center">
                          <span className="text-[#000000] text-[10px] xs:text-[12px] font-medium">Most Delayed Payments</span>
                        </div>
                      </div>
                      <div className="pt-1 flex flex-col md:flex-row md:items-center mb-2">
                        <div className="hidden md:flex items-center">
                          <div className="w-[166px] h-[30px] bg-[#FFD16F] rounded-xl flex items-center justify-center">
                            <span className="text-[#000000] text-[12px] font-medium">Most Delayed Payments</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-[11px] xs:text-[13px] text-[#000000] font-medium leading-relaxed">
                        When clients lag behind on payments, they&apos;ll show up here. For now, no delays.
                      </div>
                    </div>
                  )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ClientInsightsAccordion;
