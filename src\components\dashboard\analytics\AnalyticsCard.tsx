import Image from "next/image";
import React from "react";

export interface AnalyticsCardProps {
  color: string;
  imagePath: string;
  title: string;
  value?: string | number;
  textColor: string;
  showClients?: boolean;
  isLoading?: boolean;
  subtitle?: string;
}

const AnalyticsCard: React.FC<AnalyticsCardProps> = ({
  color,
  imagePath,
  title,
  value,
  textColor,
  showClients = false,
}) => {
  return (
    <div
      className="pl-6 rounded-base flex relative overflow-hidden min-h-[120px] transition-all duration-300 hover:shadow-lg font-poppins"
      style={{ backgroundColor: color }}
    >
      {/* Content */}
      <div className="flex-1 flex flex-col justify-center relative z-10">
        <p className="text-[16px] font-normal text-[#2D3134] mb-2 font-poppins" style={{ lineHeight: '24px' }}>
          {title}
        </p>
        <div className="flex items-baseline gap-1">
          <p
            className="text-[24px] font-semibold font-poppins"
            style={{ color: textColor }}
          >
            {value}
          </p>
          {showClients && (
            <span className="text-[14px] font-normal text-primary/60 font-poppins">
              Patients
            </span>
          )}
        </div>
      </div>

      {/* Image - positioned to touch bottom and right edges */}
      <div className="absolute right-0 bottom-0">
        <Image
          src={imagePath}
          alt={title}
          width={140}
          height={140}
        />
      </div>
    </div>
  );
};

export default AnalyticsCard;
