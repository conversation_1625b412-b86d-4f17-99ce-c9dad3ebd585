@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap');

/* The New Elegance Font */
@font-face {
  font-family: 'The New Elegance';
  src: url('/fonts/TheNewElegance-Regular.woff2') format('woff2'),
       url('/fonts/TheNewElegance-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'The New Elegance';
  src: url('/fonts/TheNewElegance-Italic.woff2') format('woff2'),
       url('/fonts/TheNewElegance-Italic.woff') format('woff');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #f5f5f7;
  --foreground: #171717;
  --font-roboto: 'Roboto', sans-serif;
  --font-new-elegance: 'The New Elegance', 'Playfair Display', 'Times New Roman', serif;
  --font-poppins: 'Poppins', sans-serif;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  color: var(--foreground);
  background: var(--background);
  position: relative;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .font-new-elegance {
    font-family: var(--font-new-elegance);
  }
}

/* scroll bar style */
::-webkit-scrollbar {
  width: 0px; /* Keep vertical scrollbar hidden */
  height: 12px; /* Made broader for better horizontal dragging */
  background-color: #f5f5f5;
}

::-webkit-scrollbar-thumb {
  background-color: #d0d1d2;
  border-radius: 6px; /* Adjusted border radius for broader horizontal scrollbar */
  min-width: 20px; /* Ensure minimum width for better horizontal grab area */
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a8a9aa; /* Darker color on hover for better visibility */
}

/* Specific styling for date picker modal horizontal scrollbars only */
.modal-container ::-webkit-scrollbar:horizontal {
  height: 12px;
  background-color: #f8f9fa;
}

.modal-container ::-webkit-scrollbar-thumb:horizontal {
  background-color: #6c757d;
  border-radius: 6px;
  min-width: 20px;
}

.modal-container ::-webkit-scrollbar-thumb:horizontal:hover {
  background-color: #495057;
}

/* Enhanced horizontal scrollbar for scrollable areas */
.time-slots-container ::-webkit-scrollbar:horizontal,
.overflow-x-auto ::-webkit-scrollbar:horizontal {
  height: 12px;
  background-color: #f1f3f4;
}

.time-slots-container ::-webkit-scrollbar-thumb:horizontal,
.overflow-x-auto ::-webkit-scrollbar-thumb:horizontal {
  background-color: #9aa0a6;
  border-radius: 6px;
  min-width: 20px;
}

.time-slots-container ::-webkit-scrollbar-thumb:horizontal:hover,
.overflow-x-auto ::-webkit-scrollbar-thumb:horizontal:hover {
  background-color: #5f6368;
}

/* Corner styling for when both scrollbars are present */
::-webkit-scrollbar-corner {
  background-color: #f1f3f4;
}

/* Firefox horizontal scrollbar styling - keep default for vertical */
* {
  scrollbar-width: thin; /* Keep vertical scrollbars thin */
  scrollbar-color: #9aa0a6 #f1f3f4;
}

/* Enhanced horizontal scrollbar for modal containers in Firefox */
.modal-container,
.time-slots-container,
.overflow-x-auto {
  scrollbar-width: auto; /* Only for horizontal scrolling areas */
  scrollbar-color: #6c757d #f8f9fa;
}

/* dash board container  */
.dashboard_container {
  max-width: 1392px;
  margin: 0 auto;
}

@media screen and (max-width: 1392px) {
  .dashboard_container {
    max-width: 1100px;
  }
}

/* moveUpDown animation  */

.animate-move {
  animation: moveUpDown 3s ease-in-out infinite;
}

@keyframes moveUpDown {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* marquee animation start */

.client_marquee_anim {
  animation: marqueeH 40s linear infinite;
}

@keyframes marqueeH {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

.client_marquee_anim_wrap:hover .client_marquee_anim {
  animation-play-state: paused;
}

.client_marquee_anim_wrap:hover .review_image {
  filter: grayscale(100%);
}

.review_card:hover .review_image {
  filter: none;
}

/* marquee animation end */

/* globals.css or a custom CSS file */
/* #nprogress .bar {
  @apply bg-yellow-600 h-3
}

#nprogress .peg {
  box-shadow: 0 0 10px #C58843, 0 0 5px #C58843;
} */

#nprogress {
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  z-index: 9999;
  position: fixed;
  pointer-events: none;
}
#nprogress .bar {
  height: 100%;
  background-color: #c58843;
  box-shadow: 0 0 2.5px #c58843;
}
#nprogress .peg {
  right: 0;
  opacity: 1;
  width: 100px;
  height: 100%;
  display: block;
  position: absolute;
  transform: rotate(3deg) translate(0px, -4px);
  box-shadow: 0 0 10px #c58843, 0 0 5px #c58843;
}

@keyframes shake {
  0% { transform: translateX(0); }
  20% { transform: translateX(-3px); }
  40% { transform: translateX(3px); }
  60% { transform: translateX(-3px); }
  80% { transform: translateX(3px); }
  100% { transform: translateX(0); }
}

.shake-animation {
  animation: shake 0.4s ease-in-out 2;
}


@keyframes leftRight {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(10px);
  }
}

.animate-left-right {
  animation: leftRight 1.2s ease-in-out infinite;
}

.__toast-container {
  z-index: 10000 !important;
}

/* For Chrome, Safari, Edge, Opera */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}
