import axiosInstance from "@/utils/axios";
import endpoints from "@/utils/endpoints";

// Define API response interface
export interface ApiResponse<T = unknown> {
  status: string;
  message: string;
  data: T;
  responseCode: number;
}

// Define consent form data interface
export interface ConsentFormData {
  patientId: string;
  patientName: string;
  patientEmail: string;
}

// Define consent form status interface
export interface ConsentFormStatus {
  patientId: string;
  status: 'pending' | 'sent' | 'accepted';
  sentAt?: string;
  acceptedAt?: string;
}

/**
 * Send consent form to a patient
 * @param consentData - Patient data for sending consent form
 * @returns Promise with API response
 */
export async function sendConsentForm(consentData: ConsentFormData): Promise<ApiResponse> {
  try {
    const response = await axiosInstance.post(
      `${endpoints.consentForm?.send || '/therapist/consent-form/send'}`,
      consentData
    );
    return response.data;
  } catch (error) {
    console.error("Error sending consent form:", error);
    throw error;
  }
}

/**
 * Get consent form status for a patient
 * @param patientId - Patient ID
 * @returns Promise with consent form status
 */
export async function getConsentFormStatus(patientId: string): Promise<ApiResponse<ConsentFormStatus>> {
  try {
    const response = await axiosInstance.get(
      `${endpoints.consentForm?.status || '/therapist/consent-form/status'}/${patientId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error getting consent form status:", error);
    throw error;
  }
}

/**
 * Get consent form statuses for multiple patients
 * @param patientIds - Array of patient IDs
 * @returns Promise with consent form statuses
 */
export async function getConsentFormStatuses(patientIds: string[]): Promise<ApiResponse<ConsentFormStatus[]>> {
  try {
    const response = await axiosInstance.post(
      `${endpoints.consentForm?.statuses || '/therapist/consent-form/statuses'}`,
      { patientIds }
    );
    return response.data;
  } catch (error) {
    console.error("Error getting consent form statuses:", error);
    throw error;
  }
}
